using UnityEngine;

public class truckrcccam : MonoBehaviour
{
    public RCC_Camera cam;
    public string truckTag = "Truck";
    public float rcccamhight;
    public float rcccamdistance;
    

    void Update()
    {
        GameObject truck = GameObject.FindWithTag(truckTag);

        if (truck != null && truck.GetComponent<RCC_CarControllerV4>().enabled == true)
        {
            cam.TPSHeight = rcccamhight;
            cam.TPSDistance = rcccamdistance;
        }
        else if (truck != null && truck.GetComponent<RCC_CarControllerV4>().enabled == false)
        {
            cam.TPSHeight = 13f;
            cam.TPSDistance = 4.7f;
        }
    }
}
